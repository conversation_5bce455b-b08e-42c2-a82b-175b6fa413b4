-- ===================================
-- منصة سندان لأعمال المحاماة - إعداد قاعدة البيانات على Supabase
-- Sanadan Law Firm Management Platform - Supabase Database Setup
-- ===================================

-- تفعيل الامتدادات المطلوبة
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- ===================================
-- 1. جدول المستأجرين (Tenants)
-- ===================================
CREATE TABLE IF NOT EXISTS tenants (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    domain VARCHAR(255) UNIQUE,
    database_name VARCHAR(255),
    logo_path VARCHAR(255),
    primary_color VARCHAR(7) DEFAULT '#1e40af',
    secondary_color VARCHAR(7) DEFAULT '#64748b',
    timezone VARCHAR(50) DEFAULT 'Asia/Kuwait',
    locale VARCHAR(10) DEFAULT 'ar',
    currency VARCHAR(3) DEFAULT 'KWD',
    date_format VARCHAR(20) DEFAULT 'd/m/Y',
    time_format VARCHAR(10) DEFAULT 'H:i',
    settings JSONB,
    is_active BOOLEAN DEFAULT true,
    trial_ends_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- فهارس جدول المستأجرين
CREATE INDEX IF NOT EXISTS idx_tenants_slug ON tenants(slug);
CREATE INDEX IF NOT EXISTS idx_tenants_domain ON tenants(domain);
CREATE INDEX IF NOT EXISTS idx_tenants_is_active ON tenants(is_active);

-- ===================================
-- 2. جدول النطاقات (Domains)
-- ===================================
CREATE TABLE IF NOT EXISTS domains (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    domain VARCHAR(255) UNIQUE NOT NULL,
    is_primary BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    ssl_enabled BOOLEAN DEFAULT false,
    ssl_certificate TEXT,
    ssl_private_key TEXT,
    verified_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- فهارس جدول النطاقات
CREATE INDEX IF NOT EXISTS idx_domains_tenant_id ON domains(tenant_id);
CREATE INDEX IF NOT EXISTS idx_domains_domain ON domains(domain);
CREATE INDEX IF NOT EXISTS idx_domains_is_primary ON domains(is_primary);

-- ===================================
-- 3. جدول المستخدمين (Users) - مع ربط Supabase Auth
-- ===================================
CREATE TABLE IF NOT EXISTS users (
    id BIGSERIAL PRIMARY KEY,
    auth_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    tenant_id BIGINT NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    email_verified_at TIMESTAMP WITH TIME ZONE,
    phone VARCHAR(20),
    avatar_path VARCHAR(255),
    role VARCHAR(50) DEFAULT 'user',
    permissions JSONB,
    is_active BOOLEAN DEFAULT true,
    last_login_at TIMESTAMP WITH TIME ZONE,
    last_login_ip INET,
    settings JSONB,
    remember_token VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- فهارس جدول المستخدمين
CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_auth_id ON users(auth_id);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);
CREATE INDEX IF NOT EXISTS idx_users_deleted_at ON users(deleted_at);

-- ===================================
-- 4. جدول مكاتب المحاماة (Law Firms)
-- ===================================
CREATE TABLE IF NOT EXISTS law_firms (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    license_number VARCHAR(100),
    commercial_registration VARCHAR(100),
    tax_number VARCHAR(100),
    address TEXT,
    phone VARCHAR(20),
    fax VARCHAR(20),
    email VARCHAR(255),
    website VARCHAR(255),
    logo_path VARCHAR(255),
    established_date DATE,
    specializations JSONB,
    description TEXT,
    bank_details JSONB,
    contact_person VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- فهارس جدول مكاتب المحاماة
CREATE INDEX IF NOT EXISTS idx_law_firms_tenant_id ON law_firms(tenant_id);
CREATE INDEX IF NOT EXISTS idx_law_firms_license_number ON law_firms(license_number);
CREATE INDEX IF NOT EXISTS idx_law_firms_is_active ON law_firms(is_active);

-- ===================================
-- 5. جدول المحامين (Lawyers)
-- ===================================
CREATE TABLE IF NOT EXISTS lawyers (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    law_firm_id BIGINT REFERENCES law_firms(id) ON DELETE SET NULL,
    license_number VARCHAR(100) UNIQUE NOT NULL,
    bar_association VARCHAR(255),
    specializations JSONB,
    experience_years INTEGER DEFAULT 0,
    education JSONB,
    certifications JSONB,
    languages JSONB,
    hourly_rate DECIMAL(8,2),
    bio TEXT,
    achievements TEXT,
    is_partner BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    joined_at DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- فهارس جدول المحامين
CREATE INDEX IF NOT EXISTS idx_lawyers_user_id ON lawyers(user_id);
CREATE INDEX IF NOT EXISTS idx_lawyers_law_firm_id ON lawyers(law_firm_id);
CREATE INDEX IF NOT EXISTS idx_lawyers_license_number ON lawyers(license_number);
CREATE INDEX IF NOT EXISTS idx_lawyers_is_active ON lawyers(is_active);

-- ===================================
-- 6. جدول العملاء (Clients)
-- ===================================
CREATE TABLE IF NOT EXISTS clients (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20),
    phone_secondary VARCHAR(20),
    address TEXT,
    id_number VARCHAR(50),
    passport_number VARCHAR(50),
    client_type VARCHAR(20) DEFAULT 'individual' CHECK (client_type IN ('individual', 'company', 'government')),
    company_name VARCHAR(255),
    commercial_registration VARCHAR(100),
    birth_date DATE,
    gender VARCHAR(10) CHECK (gender IN ('male', 'female')),
    nationality VARCHAR(100),
    occupation VARCHAR(255),
    notes TEXT,
    emergency_contacts JSONB,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'blacklisted')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- فهارس جدول العملاء
CREATE INDEX IF NOT EXISTS idx_clients_tenant_id ON clients(tenant_id);
CREATE INDEX IF NOT EXISTS idx_clients_email ON clients(email);
CREATE INDEX IF NOT EXISTS idx_clients_phone ON clients(phone);
CREATE INDEX IF NOT EXISTS idx_clients_id_number ON clients(id_number);
CREATE INDEX IF NOT EXISTS idx_clients_client_type ON clients(client_type);
CREATE INDEX IF NOT EXISTS idx_clients_status ON clients(status);
CREATE INDEX IF NOT EXISTS idx_clients_deleted_at ON clients(deleted_at);

-- ===================================
-- 7. جدول أنواع القضايا (Case Types)
-- ===================================
CREATE TABLE IF NOT EXISTS case_types (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    description TEXT,
    color VARCHAR(7) DEFAULT '#3b82f6',
    icon VARCHAR(50),
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- فهارس جدول أنواع القضايا
CREATE INDEX IF NOT EXISTS idx_case_types_tenant_id ON case_types(tenant_id);
CREATE INDEX IF NOT EXISTS idx_case_types_is_active ON case_types(is_active);

-- ===================================
-- 8. جدول حالات القضايا (Case Statuses)
-- ===================================
CREATE TABLE IF NOT EXISTS case_statuses (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    description TEXT,
    color VARCHAR(7) DEFAULT '#10b981',
    icon VARCHAR(50),
    is_final BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- فهارس جدول حالات القضايا
CREATE INDEX IF NOT EXISTS idx_case_statuses_tenant_id ON case_statuses(tenant_id);
CREATE INDEX IF NOT EXISTS idx_case_statuses_is_active ON case_statuses(is_active);

-- ===================================
-- 9. جدول المحاكم (Courts)
-- ===================================
CREATE TABLE IF NOT EXISTS courts (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    court_type VARCHAR(50) DEFAULT 'first_instance' CHECK (court_type IN ('first_instance', 'appeal', 'cassation', 'constitutional', 'administrative', 'specialized')),
    address TEXT,
    phone VARCHAR(20),
    fax VARCHAR(20),
    email VARCHAR(255),
    website VARCHAR(255),
    working_hours JSONB,
    location_coordinates POINT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- فهارس جدول المحاكم
CREATE INDEX IF NOT EXISTS idx_courts_tenant_id ON courts(tenant_id);
CREATE INDEX IF NOT EXISTS idx_courts_court_type ON courts(court_type);
CREATE INDEX IF NOT EXISTS idx_courts_is_active ON courts(is_active);

-- ===================================
-- 10. جدول دوائر المحاكم (Court Chambers)
-- ===================================
CREATE TABLE IF NOT EXISTS court_chambers (
    id BIGSERIAL PRIMARY KEY,
    court_id BIGINT NOT NULL REFERENCES courts(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    chamber_number VARCHAR(50),
    judge_name VARCHAR(255) NOT NULL,
    judge_title VARCHAR(100),
    clerk_name VARCHAR(255),
    specialization VARCHAR(50) DEFAULT 'general' CHECK (specialization IN ('civil', 'commercial', 'criminal', 'family', 'administrative', 'labor', 'real_estate', 'inheritance', 'general')),
    working_days JSONB,
    session_times JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- فهارس جدول دوائر المحاكم
CREATE INDEX IF NOT EXISTS idx_court_chambers_court_id ON court_chambers(court_id);
CREATE INDEX IF NOT EXISTS idx_court_chambers_specialization ON court_chambers(specialization);
CREATE INDEX IF NOT EXISTS idx_court_chambers_is_active ON court_chambers(is_active);

-- ===================================
-- 11. جدول القضايا (Cases)
-- ===================================
CREATE TABLE IF NOT EXISTS cases (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    case_number VARCHAR(100) NOT NULL,
    court_case_number VARCHAR(100),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    case_type_id BIGINT NOT NULL REFERENCES case_types(id) ON DELETE RESTRICT,
    case_status_id BIGINT NOT NULL REFERENCES case_statuses(id) ON DELETE RESTRICT,
    priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    client_id BIGINT NOT NULL REFERENCES clients(id) ON DELETE RESTRICT,
    lawyer_id BIGINT NOT NULL REFERENCES users(id) ON DELETE RESTRICT,
    court_id BIGINT REFERENCES courts(id) ON DELETE SET NULL,
    court_chamber_id BIGINT REFERENCES court_chambers(id) ON DELETE SET NULL,
    filing_date DATE,
    first_hearing_date DATE,
    expected_completion_date DATE,
    case_value DECIMAL(12,2),
    fees DECIMAL(10,2),
    court_fees DECIMAL(10,2),
    legal_basis TEXT,
    client_demands TEXT,
    opponent_info JSONB,
    case_parties JSONB,
    related_laws TEXT,
    notes TEXT,
    is_confidential BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- فهارس جدول القضايا
CREATE INDEX IF NOT EXISTS idx_cases_tenant_id ON cases(tenant_id);
CREATE INDEX IF NOT EXISTS idx_cases_case_number ON cases(case_number);
CREATE INDEX IF NOT EXISTS idx_cases_court_case_number ON cases(court_case_number);
CREATE INDEX IF NOT EXISTS idx_cases_case_type_id ON cases(case_type_id);
CREATE INDEX IF NOT EXISTS idx_cases_case_status_id ON cases(case_status_id);
CREATE INDEX IF NOT EXISTS idx_cases_client_id ON cases(client_id);
CREATE INDEX IF NOT EXISTS idx_cases_lawyer_id ON cases(lawyer_id);
CREATE INDEX IF NOT EXISTS idx_cases_court_id ON cases(court_id);
CREATE INDEX IF NOT EXISTS idx_cases_priority ON cases(priority);
CREATE INDEX IF NOT EXISTS idx_cases_filing_date ON cases(filing_date);
CREATE INDEX IF NOT EXISTS idx_cases_deleted_at ON cases(deleted_at);

-- فهرس فريد لرقم القضية لكل مستأجر
CREATE UNIQUE INDEX IF NOT EXISTS idx_cases_case_number_tenant ON cases(case_number, tenant_id) WHERE deleted_at IS NULL;

-- ===================================
-- 12. جدول الجلسات (Hearings)
-- ===================================
CREATE TABLE IF NOT EXISTS hearings (
    id BIGSERIAL PRIMARY KEY,
    case_id BIGINT NOT NULL REFERENCES cases(id) ON DELETE CASCADE,
    court_chamber_id BIGINT NOT NULL REFERENCES court_chambers(id) ON DELETE RESTRICT,
    hearing_date DATE NOT NULL,
    hearing_time TIME NOT NULL,
    type VARCHAR(20) DEFAULT 'continuation' CHECK (type IN ('first', 'continuation', 'final', 'verdict', 'appeal', 'execution', 'mediation', 'expert', 'witness')),
    status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'completed', 'postponed', 'cancelled', 'no_show')),
    title VARCHAR(255),
    description TEXT,
    agenda TEXT,
    client_attendance BOOLEAN,
    lawyer_attendance BOOLEAN,
    opponent_attendance BOOLEAN,
    judge_notes TEXT,
    lawyer_notes TEXT,
    next_hearing_date DATE,
    next_hearing_time TIME,
    documents_required JSONB,
    outcome TEXT,
    postponement_reason TEXT,
    reminder_sent BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- فهارس جدول الجلسات
CREATE INDEX IF NOT EXISTS idx_hearings_case_id ON hearings(case_id);
CREATE INDEX IF NOT EXISTS idx_hearings_court_chamber_id ON hearings(court_chamber_id);
CREATE INDEX IF NOT EXISTS idx_hearings_hearing_date ON hearings(hearing_date);
CREATE INDEX IF NOT EXISTS idx_hearings_status ON hearings(status);
CREATE INDEX IF NOT EXISTS idx_hearings_type ON hearings(type);
CREATE INDEX IF NOT EXISTS idx_hearings_reminder_sent ON hearings(reminder_sent);

-- ===================================
-- 13. جدول المهام (Tasks)
-- ===================================
CREATE TABLE IF NOT EXISTS tasks (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    case_id BIGINT REFERENCES cases(id) ON DELETE CASCADE,
    assigned_to BIGINT NOT NULL REFERENCES users(id) ON DELETE RESTRICT,
    assigned_by BIGINT REFERENCES users(id) ON DELETE SET NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'cancelled', 'on_hold')),
    due_date DATE,
    due_time TIME,
    estimated_hours DECIMAL(5,2),
    actual_hours DECIMAL(5,2),
    completion_percentage INTEGER DEFAULT 0 CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
    tags JSONB,
    attachments JSONB,
    notes TEXT,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- فهارس جدول المهام
CREATE INDEX IF NOT EXISTS idx_tasks_tenant_id ON tasks(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tasks_case_id ON tasks(case_id);
CREATE INDEX IF NOT EXISTS idx_tasks_assigned_to ON tasks(assigned_to);
CREATE INDEX IF NOT EXISTS idx_tasks_assigned_by ON tasks(assigned_by);
CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status);
CREATE INDEX IF NOT EXISTS idx_tasks_priority ON tasks(priority);
CREATE INDEX IF NOT EXISTS idx_tasks_due_date ON tasks(due_date);
CREATE INDEX IF NOT EXISTS idx_tasks_deleted_at ON tasks(deleted_at);

-- ===================================
-- 14. جدول المستندات (Documents)
-- ===================================
CREATE TABLE IF NOT EXISTS documents (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    case_id BIGINT REFERENCES cases(id) ON DELETE CASCADE,
    client_id BIGINT REFERENCES clients(id) ON DELETE CASCADE,
    uploaded_by BIGINT NOT NULL REFERENCES users(id) ON DELETE RESTRICT,
    name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_extension VARCHAR(10),
    document_type VARCHAR(50) DEFAULT 'other' CHECK (document_type IN ('contract', 'power_of_attorney', 'legal_notice', 'petition', 'memorandum', 'agreement', 'letter', 'invoice', 'receipt', 'report', 'evidence', 'court_document', 'other')),
    category VARCHAR(50) DEFAULT 'general' CHECK (category IN ('civil', 'commercial', 'criminal', 'family', 'administrative', 'labor', 'real_estate', 'inheritance', 'general')),
    description TEXT,
    tags JSONB,
    is_confidential BOOLEAN DEFAULT false,
    is_signed BOOLEAN DEFAULT false,
    version INTEGER DEFAULT 1,
    parent_document_id BIGINT REFERENCES documents(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- فهارس جدول المستندات
CREATE INDEX IF NOT EXISTS idx_documents_tenant_id ON documents(tenant_id);
CREATE INDEX IF NOT EXISTS idx_documents_case_id ON documents(case_id);
CREATE INDEX IF NOT EXISTS idx_documents_client_id ON documents(client_id);
CREATE INDEX IF NOT EXISTS idx_documents_uploaded_by ON documents(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_documents_document_type ON documents(document_type);
CREATE INDEX IF NOT EXISTS idx_documents_category ON documents(category);
CREATE INDEX IF NOT EXISTS idx_documents_parent_document_id ON documents(parent_document_id);
CREATE INDEX IF NOT EXISTS idx_documents_deleted_at ON documents(deleted_at);

-- ===================================
-- 15. جدول قوالب المستندات (Document Templates)
-- ===================================
CREATE TABLE IF NOT EXISTS document_templates (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    content TEXT NOT NULL,
    type VARCHAR(50) DEFAULT 'other' CHECK (type IN ('contract', 'power_of_attorney', 'legal_notice', 'petition', 'memorandum', 'agreement', 'letter', 'invoice', 'receipt', 'report', 'other')),
    category VARCHAR(50) DEFAULT 'general' CHECK (category IN ('civil', 'commercial', 'criminal', 'family', 'administrative', 'labor', 'real_estate', 'inheritance', 'general')),
    variables JSONB,
    is_active BOOLEAN DEFAULT true,
    created_by BIGINT NOT NULL REFERENCES users(id) ON DELETE RESTRICT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- فهارس جدول قوالب المستندات
CREATE INDEX IF NOT EXISTS idx_document_templates_tenant_id ON document_templates(tenant_id);
CREATE INDEX IF NOT EXISTS idx_document_templates_type ON document_templates(type);
CREATE INDEX IF NOT EXISTS idx_document_templates_category ON document_templates(category);
CREATE INDEX IF NOT EXISTS idx_document_templates_is_active ON document_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_document_templates_created_by ON document_templates(created_by);

-- ===================================
-- 16. جدول الفواتير (Invoices)
-- ===================================
CREATE TABLE IF NOT EXISTS invoices (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    case_id BIGINT REFERENCES cases(id) ON DELETE SET NULL,
    client_id BIGINT NOT NULL REFERENCES clients(id) ON DELETE RESTRICT,
    invoice_number VARCHAR(100) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    issue_date DATE NOT NULL,
    due_date DATE NOT NULL,
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0,
    tax_rate DECIMAL(5,2) DEFAULT 0,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    discount_rate DECIMAL(5,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    paid_amount DECIMAL(10,2) DEFAULT 0,
    balance_due DECIMAL(10,2) DEFAULT 0,
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'sent', 'viewed', 'partial', 'paid', 'overdue', 'cancelled')),
    payment_terms TEXT,
    notes TEXT,
    items JSONB NOT NULL,
    sent_at TIMESTAMP WITH TIME ZONE,
    paid_at TIMESTAMP WITH TIME ZONE,
    created_by BIGINT NOT NULL REFERENCES users(id) ON DELETE RESTRICT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- فهارس جدول الفواتير
CREATE INDEX IF NOT EXISTS idx_invoices_tenant_id ON invoices(tenant_id);
CREATE INDEX IF NOT EXISTS idx_invoices_case_id ON invoices(case_id);
CREATE INDEX IF NOT EXISTS idx_invoices_client_id ON invoices(client_id);
CREATE INDEX IF NOT EXISTS idx_invoices_invoice_number ON invoices(invoice_number);
CREATE INDEX IF NOT EXISTS idx_invoices_status ON invoices(status);
CREATE INDEX IF NOT EXISTS idx_invoices_issue_date ON invoices(issue_date);
CREATE INDEX IF NOT EXISTS idx_invoices_due_date ON invoices(due_date);
CREATE INDEX IF NOT EXISTS idx_invoices_created_by ON invoices(created_by);
CREATE INDEX IF NOT EXISTS idx_invoices_deleted_at ON invoices(deleted_at);

-- فهرس فريد لرقم الفاتورة لكل مستأجر
CREATE UNIQUE INDEX IF NOT EXISTS idx_invoices_invoice_number_tenant ON invoices(invoice_number, tenant_id) WHERE deleted_at IS NULL;
